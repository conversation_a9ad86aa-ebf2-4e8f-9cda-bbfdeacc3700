# MES智能体测试脚本
# PowerShell脚本，用于测试LangChain API

Write-Host "🚀 开始测试MES咨询专家智能体..." -ForegroundColor Green

# 服务器地址
$baseUrl = "http://localhost:64922"

Write-Host "`n📋 1. 测试健康检查..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/health" -Method Get
    Write-Host "✅ 健康检查结果: $healthResponse" -ForegroundColor Green
} catch {
    Write-Host "❌ 健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n📋 2. 测试快速发送接口..." -ForegroundColor Yellow
try {
    $quickMessage = "我们是一家汽车零部件制造企业，想了解MES系统能为我们带来什么价值？"
    
    $quickResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/quick-send?userId=test_user_001" `
        -Method Post `
        -ContentType "application/json" `
        -Body (ConvertTo-Json $quickMessage)
    
    Write-Host "✅ 快速发送成功!" -ForegroundColor Green
    Write-Host "📝 AI回复: $($quickResponse.content)" -ForegroundColor Cyan
    Write-Host "🆔 会话ID: $($quickResponse.sessionId)" -ForegroundColor Magenta
} catch {
    Write-Host "❌ 快速发送失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n📋 3. 测试完整消息接口..." -ForegroundColor Yellow
try {
    $fullMessage = @{
        content = "我是生产经理，我们工厂经常出现生产瓶颈，MES系统如何帮助我们解决这个问题？"
        userId = "production_manager_001"
        enableMemory = $true
        systemPrompt = "你是一位资深的MES系统咨询专家，专门帮助制造企业优化生产管理流程。"
    }
    
    $fullResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/send-message" `
        -Method Post `
        -ContentType "application/json" `
        -Body (ConvertTo-Json $fullMessage)
    
    Write-Host "✅ 完整消息发送成功!" -ForegroundColor Green
    Write-Host "📝 AI回复: $($fullResponse.content)" -ForegroundColor Cyan
    Write-Host "🆔 会话ID: $($fullResponse.sessionId)" -ForegroundColor Magenta
    Write-Host "⏱️ 处理时间: $($fullResponse.processingTimeMs)ms" -ForegroundColor Blue
    
    # 保存会话ID用于后续测试
    $sessionId = $fullResponse.sessionId
    
    Write-Host "`n📋 4. 测试会话记忆功能..." -ForegroundColor Yellow
    
    $followUpMessage = @{
        content = "具体实施步骤是什么？大概需要多长时间？"
        userId = "production_manager_001"
        enableMemory = $true
        sessionId = $sessionId
    }
    
    $followUpResponse = Invoke-RestMethod -Uri "$baseUrl/api/LangChain/send-message" `
        -Method Post `
        -ContentType "application/json" `
        -Body (ConvertTo-Json $followUpMessage)
    
    Write-Host "✅ 会话记忆测试成功!" -ForegroundColor Green
    Write-Host "📝 AI回复: $($followUpResponse.content)" -ForegroundColor Cyan
    Write-Host "🆔 会话ID: $($followUpResponse.sessionId)" -ForegroundColor Magenta
    
} catch {
    Write-Host "❌ 完整消息测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n🎉 测试完成！" -ForegroundColor Green
Write-Host "💡 提示：你可以在Swagger UI中继续测试更多功能" -ForegroundColor Yellow
Write-Host "🌐 Swagger地址: $baseUrl/swagger" -ForegroundColor Blue
